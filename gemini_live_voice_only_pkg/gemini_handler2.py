import asyncio
import base64
import numpy as np
from typing import <PERSON><PERSON><PERSON><PERSON><PERSON>, Literal, Optional
from google import genai
from google.genai import types
from google.genai.types import (
    LiveConnectConfig,
    PrebuiltVoiceConfig,
    SpeechConfig,
    VoiceConfig,
    Content,
    Part,
)
from fastrtc import AsyncStreamHandler, wait_for_item
from gemini_live_voice_only_pkg.tools import search_products, search_products_func

class GeminiHandler(AsyncStreamHandler):
    """Handler for interacting with the Gemini API."""
    
    def __init__(
        self,
        api_key: str,
        system_prompt: str,
        voice_name: str,
        expected_layout: Literal["mono"] = "mono",
        output_sample_rate: int = 24000,
        output_frame_size: int = 480,
        input_sample_rate: int = 16000,
    ) -> None:
        super().__init__(expected_layout, output_sample_rate, output_frame_size, input_sample_rate=input_sample_rate)
        self.api_key = api_key
        self.system_prompt = system_prompt
        self.voice_name = voice_name
        self.input_queue: asyncio.Queue = asyncio.Queue()
        self.output_queue: asyncio.Queue = asyncio.Queue()
        self.quit: asyncio.Event = asyncio.Event()
        self.session: Optional[genai.types.LiveSession] = None
        self.client: Optional[genai.Client] = None
        self._session_task: Optional[asyncio.Task] = None

    def copy(self) -> "GeminiHandler":
        return GeminiHandler(
            api_key=self.api_key,
            system_prompt=self.system_prompt,
            voice_name=self.voice_name,
            expected_layout="mono",
            output_sample_rate=self.output_sample_rate,
            output_frame_size=self.output_frame_size,
            input_sample_rate=self.input_sample_rate,
        )

    async def start_up(self):
        """Initialize the Gemini client and start the session task."""
        try:
            self.client = genai.Client(
                api_key=self.api_key,
                http_options={"api_version": "v1beta"},
            )
            
            # Start the session handling task
            self._session_task = asyncio.create_task(self._handle_session())
            print("Gemini handler started successfully")
            
        except Exception as e:
            print(f"Error starting Gemini handler: {e}")
            raise

    async def _handle_session(self):
        """Handle the Gemini session lifecycle."""
        while not self.quit.is_set():
            try:
                config = LiveConnectConfig(
                    response_modalities=["AUDIO"],
                    system_instruction=Content(
                        parts=[Part(text=self.system_prompt)]
                    ),
                    media_resolution="MEDIA_RESOLUTION_MEDIUM",
                    speech_config=SpeechConfig(
                        voice_config=VoiceConfig(
                            prebuilt_voice_config=PrebuiltVoiceConfig(
                                voice_name=self.voice_name,
                            )
                        )
                    ),
                    context_window_compression=types.ContextWindowCompressionConfig(
                        trigger_tokens=25600,
                        sliding_window=types.SlidingWindow(target_tokens=12800),
                    ),
                    tools=[search_products],
                    output_audio_transcription={},
                    realtime_input_config=types.RealtimeInputConfig(
                        automatic_activity_detection=types.AutomaticActivityDetection(
                            start_of_speech_sensitivity=types.StartSensitivity.START_SENSITIVITY_LOW,
                            end_of_speech_sensitivity=types.EndSensitivity.END_SENSITIVITY_LOW,
                            prefix_padding_ms=200,
                            silence_duration_ms=500
                        ),
                        activity_handling=types.ActivityHandling.NO_INTERRUPTION
                    )
                )
                
                async with self.client.aio.live.connect(
                    model="models/gemini-2.5-flash-preview-native-audio-dialog", 
                    config=config
                ) as session:
                    self.session = session
                    print("Connected to Gemini session")
                    
                    # Use the unified stream handler
                    await self._handle_unified_stream(session)
                    
            except Exception as e:
                print(f"Session error: {e}")
                if not self.quit.is_set():
                    print("Reconnecting in 2 seconds...")
                    await asyncio.sleep(2)
                    
        self.session = None

    async def _handle_unified_stream(self, session):
        """
        Handle all communication through start_stream.
        The start_stream method returns all types of responses including audio and tool calls.
        """
        try:
            # Start the bidirectional stream
            async for response in session.start_stream(
                stream=self._audio_stream(), 
                mime_type="audio/pcm"
            ):
                if self.quit.is_set():
                    break
                
                # The response from start_stream can contain different types of data
                
                # Handle audio data
                if hasattr(response, 'data') and response.data:
                    try:
                        # Audio data comes as bytes
                        array = np.frombuffer(response.data, dtype=np.int16)
                        
                        # Add to output queue with queue management
                        if self.output_queue.qsize() < 10:
                            self.output_queue.put_nowait((self.output_sample_rate, array))
                        else:
                            # Replace oldest with newest
                            try:
                                self.output_queue.get_nowait()
                            except asyncio.QueueEmpty:
                                pass
                            self.output_queue.put_nowait((self.output_sample_rate, array))
                            
                    except Exception as e:
                        print(f"Error processing audio data: {e}")
                
                # Handle tool calls if they come through the stream
                # The exact structure depends on how Gemini returns tool calls in the stream
                if hasattr(response, 'tool_call') and response.tool_call:
                    await self._handle_tool_call_inline(session, response.tool_call)
                
                # Handle text responses if present
                if hasattr(response, 'text') and response.text:
                    print(f"Text response: {response.text}")
                
                # Handle server content (another possible response format)
                if hasattr(response, 'server_content'):
                    await self._handle_server_content(session, response.server_content)
                    
        except Exception as e:
            print(f"Error in unified stream handler: {e}")
            raise

    async def _handle_server_content(self, session, server_content):
        """Handle server content which might contain tool calls."""
        try:
            if hasattr(server_content, 'model_turn'):
                model_turn = server_content.model_turn
                if hasattr(model_turn, 'parts'):
                    for part in model_turn.parts:
                        # Check for function calls in parts
                        if hasattr(part, 'function_call'):
                            await self._handle_function_call(session, part.function_call)
                        # Check for executable code
                        if hasattr(part, 'executable_code'):
                            print(f"Executable code: {part.executable_code}")
                        # Check for code execution result
                        if hasattr(part, 'code_execution_result'):
                            print(f"Code result: {part.code_execution_result}")
                            
        except Exception as e:
            print(f"Error handling server content: {e}")

    async def _handle_tool_call_inline(self, session, tool_call):
        """Handle tool calls that come through the stream."""
        try:
            function_responses = []
            for fc in tool_call.function_calls:
                print(f"Tool call: {fc.name} with args: {fc.args}")
                
                if fc.name == "search_products":
                    try:
                        result = search_products_func(**fc.args)
                        function_response = types.FunctionResponse(
                            id=fc.id,
                            name=fc.name,
                            response=result
                        )
                        function_responses.append(function_response)
                    except Exception as e:
                        print(f"Error executing function {fc.name}: {e}")
            
            if function_responses:
                # Send tool response back to the session
                await session.send_tool_response(function_responses=function_responses)
                
        except Exception as e:
            print(f"Error handling tool call: {e}")

    async def _handle_function_call(self, session, function_call):
        """Handle individual function calls."""
        try:
            print(f"Function call: {function_call.name} with args: {function_call.args}")
            
            if function_call.name == "search_products":
                result = search_products_func(**function_call.args)
                
                function_response = types.FunctionResponse(
                    id=function_call.id,
                    name=function_call.name,
                    response=result
                )
                
                # Send the response
                await session.send_tool_response(function_responses=[function_response])
                
        except Exception as e:
            print(f"Error handling function call: {e}")

    async def _audio_stream(self) -> AsyncGenerator[bytes, None]:
        """Generator that yields audio data from the input queue."""
        while not self.quit.is_set():
            try:
                # Wait for audio data with timeout
                audio_bytes = await asyncio.wait_for(self.input_queue.get(), timeout=0.05)
                
                if audio_bytes and not self.quit.is_set():
                    yield audio_bytes
                else:
                    # Yield empty bytes to keep stream alive
                    yield b''
                    
            except asyncio.TimeoutError:
                # No audio data available, yield empty bytes to keep the stream alive
                yield b''
                continue
            except Exception as e:
                print(f"Error in audio stream: {e}")
                # Yield empty bytes to prevent stream from breaking
                yield b''
                continue

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio frame from FastRTC and add to input queue."""
        try:
            sample_rate, array = frame
            
            # Skip if shutting down
            if self.quit.is_set():
                return
                
            # Ensure audio is in the correct format
            if array.dtype != np.int16:
                # Convert to int16 with proper scaling
                if array.dtype == np.float32 or array.dtype == np.float64:
                    array = (array * 32767).astype(np.int16)
                else:
                    array = array.astype(np.int16)
                
            # Ensure mono audio
            if array.ndim > 1:
                array = array.squeeze()
                if array.ndim > 1:  # Still multi-dimensional, take first channel
                    array = array[0] if array.shape[0] < array.shape[1] else array[:, 0]
            
            # Convert to bytes (PCM format expected by Gemini)
            audio_bytes = array.tobytes()
            
            # Add to queue with overflow management
            if self.input_queue.qsize() < 10:  # Limit queue size
                self.input_queue.put_nowait(audio_bytes)
            else:
                # Drop oldest frame and add new one
                try:
                    self.input_queue.get_nowait()  # Remove oldest
                except asyncio.QueueEmpty:
                    pass
                self.input_queue.put_nowait(audio_bytes)  # Add newest
                
        except Exception as e:
            print(f"Error receiving audio frame: {e}")
            # Don't re-raise to prevent crashing the WebRTC connection

    async def emit(self) -> tuple[int, np.ndarray] | None:
        """Emit audio frame to FastRTC."""
        try:
            return await wait_for_item(self.output_queue)
        except Exception as e:
            print(f"Error emitting audio frame: {e}")
            return None

    def shutdown(self) -> None:
        """Shutdown the handler and clean up resources."""
        print("Shutting down Gemini handler...")
        self.quit.set()
        
        # Cancel the session task
        if self._session_task and not self._session_task.done():
            self._session_task.cancel()
            
        # Clear the session reference
        self.session = None
        
        # Clear queues to prevent memory leaks
        self._clear_queue(self.input_queue)
        self._clear_queue(self.output_queue)
        
        print("Gemini handler shutdown complete")

    def _clear_queue(self, queue: asyncio.Queue):
        """Safely clear an asyncio queue."""
        while not queue.empty():
            try:
                queue.get_nowait()
            except asyncio.QueueEmpty:
                break